<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      modalWidth="800"
      @cancel="cancel"
      modalHeight="700"
    >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="预案名称" prop="emergencyPlanName">
              <a-input v-model="form.emergencyPlanName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="预案类型" prop="emergencyPlanType">
              <a-select show-search placeholder="请输入" v-model="form.emergencyPlanType" option-filter-prop="children">
                <a-select-option v-for="item in emergencyTypeOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="批复时间" prop="replyTime">
              <a-date-picker v-model="form.replyTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="负责人" prop="chargeName">
              <a-input v-model="form.chargeName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
                @change="onProjectChange"
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="批文附件">
              <UploadFile
                :fileUrl.sync="form.approvalAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="预案文件附件">
              <UploadFile
                :fileUrl.sync="form.planAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          
          <!-- 响应级别区块 -->
          <a-col :lg="24" :md="24" :sm="24" v-for="(level, index) in responseLevels" :key="level.key" class="response-level-block">
            <div class="response-level-container">
              <h3 class="response-level-title">{{ level.name }}</h3>

              <!-- 站点选择 -->
              <div class="site-selection">
                <label class="site-label">站点：</label>
                <a-select
                  v-model="form.responseLevels[level.key].siteId"
                  placeholder="请选择站点"
                  allowClear
                  style="width: 100%"
                  :disabled="!form.projectId"
                  @change="(siteId) => onSiteChange(level.key, siteId)"
                >
                  <a-select-option
                    v-for="site in siteOptions"
                    :key="site.siteId"
                    :value="site.siteId"
                  >
                    {{ site.siteName }}
                  </a-select-option>
                </a-select>
              </div>

              <div class="response-content">
                <label class="response-label">响应内容：</label>
                <a-textarea
                  v-model="form.responseLevels[level.key].content"
                  placeholder="请输入响应内容"
                  :rows="2"
                  class="response-textarea"
                />
              </div>
              
              <div class="response-config">
                <h4 class="config-title">响应配置：</h4>
                <div class="config-table">
                  <div class="table-header">
                    <div class="table-cell header-cell">规则配置</div>
                    <div class="table-cell header-cell">报警内容</div>
                    <div class="table-cell header-cell">接收人</div>
                  </div>

                  <div
                    class="table-row"
                    v-for="(config, configIndex) in form.responseLevels[level.key].configs"
                    :key="configIndex"
                  >
                    <div class="table-cell">
                      <a-button
                        type="link"
                        @click="openRuleConfig(level.key, configIndex)"
                        class="rule-config-btn"
                      >
                        规则配置
                      </a-button>
                    </div>
                    <div class="table-cell">
                      <a-input
                        v-model="config.alarmContent"
                        placeholder="请输入报警内容"
                        size="small"
                      />
                    </div>
                    <div class="table-cell">
                      <a-input
                        v-model="config.receiver"
                        placeholder="请输入接收人"
                        size="small"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
  
  <!-- 规则配置弹窗 -->
  <ConfigThreshold
    ref="configThreshold"
    :isEmergencyPlan="true"
    @ok="onRuleConfigSave"
    @close="() => {}"
  />
  </div>
</template>
<script lang="jsx">
  import { addPlan, editPlan, getPlanById, getSiteList } from '../services'
  import { getOptions, getProjectTree } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'
  import ConfigThreshold from '@/views/early-warning/ruleConfiguration/modules/ConfigThreshold.vue'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile, ConfigThreshold },
    props: ['projectOptions', 'emergencyTypeOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        tabKey: '1',
        tabPosition: 'left',
        tabAttachKey: '1',
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        siteOptions: [], // 站点选项
        currentRuleConfig: null, // 当前规则配置的上下文
        responseLevels: [
          { key: 'level1', name: 'I级响应' },
          { key: 'level2', name: 'II级响应' },
          { key: 'level3', name: 'III级响应' },
          { key: 'level4', name: 'IV级响应' },
        ],
        form: {
          approvalAttaches: [],
          chargeName: '',
          emergencyPlanId: null,
          emergencyPlanName: '',
          emergencyPlanType: null,
          planAttaches: [],
          projectId: null,
          replyTime: '',
          responseLevels: {
            level1: {
              content: '',
              siteId: null,
              siteObjectId: null, // 添加站点的objectId
              configs: [{ alarmContent: '', receiver: '', ruleConfig: null }]
            },
            level2: {
              content: '',
              siteId: null,
              siteObjectId: null, // 添加站点的objectId
              configs: [{ alarmContent: '', receiver: '', ruleConfig: null }]
            },
            level3: {
              content: '',
              siteId: null,
              siteObjectId: null, // 添加站点的objectId
              configs: [{ alarmContent: '', receiver: '', ruleConfig: null }]
            },
            level4: {
              content: '',
              siteId: null,
              siteObjectId: null, // 添加站点的objectId
              configs: [{ alarmContent: '', receiver: '', ruleConfig: null }]
            }
          }
        },
        open: false,
        rules: {
          emergencyPlanName: [{ required: true, message: '预案名称不能为空', trigger: 'blur' }],
          emergencyPlanType: [{ required: true, message: '预案类型不能为空', trigger: 'change' }],
          chargeName: [{ required: true, message: '负责人不能为空', trigger: 'blur' }],
          replyTime: [{ required: true, message: '批复时间不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
        },
      }
    },
    created() {
      // 初始化时获取项目列表
      this.getProjectOptions()
    },
    mounted() {
      console.log('FormDrawer mounted, refs:', this.$refs)
      this.$nextTick(() => {
        console.log('FormDrawer nextTick, refs:', this.$refs)
      })
    },
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.form.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.formTitle = '修改'
          this.modalLoading = true
          getPlanById({ emergencyPlanId: row.emergencyPlanId }).then(res => {
            if (res.code == 200) {
              this.form = {
                ...res.data,
                approvalAttaches: res.data.approvalAttaches?.map(el => el.attachUrl),
                planAttaches: res.data.planAttaches?.map(el => el.attachUrl),
              }
              this.form.emergencyPlanType = String(this.form.emergencyPlanType)
              this.modalLoading = false
            }
          })
        }
      },

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            // 组装提交数据
            const submitData = this.buildSubmitData()

            if (this.form.emergencyPlanId == null) {
              addPlan(submitData)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editPlan(submitData)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },

      // 构建提交数据
      buildSubmitData() {
        const emergencyResEvents = []

        // 遍历响应级别，构建emergencyResEvents数据
        Object.keys(this.form.responseLevels).forEach((levelKey, index) => {
          const level = this.form.responseLevels[levelKey]
          const resLevel = index + 1 // I级=1, II级=2, III级=3, IV级=4

          level.configs.forEach(config => {
            if (config.ruleConfig && (config.alarmContent || config.receiver)) {
              const resEvent = {
                logicOperator: config.ruleConfig.logicOperator || 'and',
                recUserIds: config.receiver || '',
                resContent: level.content || '',
                resLevel: resLevel,
                rules: config.ruleConfig.rules || [],
                warnContent: config.alarmContent || ''
              }
              emergencyResEvents.push(resEvent)
            }
          })
        })

        return {
          approvalAttaches: this.form.approvalAttaches || [],
          chargeName: this.form.chargeName || '',
          emergencyPlanName: this.form.emergencyPlanName || '',
          emergencyPlanType: parseInt(this.form.emergencyPlanType) || 0,
          emergencyResEvents: emergencyResEvents,
          planAttaches: this.form.planAttaches || [],
          projectId: this.form.projectId || 0,
          replyTime: this.form.replyTime ? moment(this.form.replyTime).format('YYYY-MM-DD') : '',
          ...(this.form.emergencyPlanId && { emergencyPlanId: this.form.emergencyPlanId })
        }
      },
      

      
      // 获取项目选项
      getProjectOptions() {
        if (!this.projectOptions || this.projectOptions.length === 0) {
          getProjectTree({ objectCategoryId: undefined }).then(res => {
            this.$emit('updateProjectOptions', res.data)
          })
        }
      },

      // 项目变更时的处理
      onProjectChange(projectId) {
        if (projectId) {
          this.getSiteOptions(projectId)
        } else {
          this.siteOptions = []
          // 清空所有响应级别的站点选择
          Object.keys(this.form.responseLevels).forEach(key => {
            this.form.responseLevels[key].siteId = null
            this.form.responseLevels[key].siteObjectId = null
          })
        }
      },

      // 站点变更时的处理
      onSiteChange(levelKey, siteId) {
        if (siteId) {
          // 查找选中站点的完整信息
          const selectedSite = this.siteOptions.find(site => site.siteId === siteId)
          if (selectedSite && selectedSite.objectId) {
            this.form.responseLevels[levelKey].siteObjectId = selectedSite.objectId
          }
        } else {
          this.form.responseLevels[levelKey].siteObjectId = null
        }
      },

      // 获取站点选项
      getSiteOptions(projectId) {
        const params = {
          districtCode: "",
          excludeIds: [],
          includeIds: [],
          objectCategoryId: 4,
          orgId: 0,
          riverSystemCategoryId: null,
          siteCode: "",
          // projectId: projectId,
          siteName: "",
          pageNum: 1,
          pageSize: 9999,
          groupId: 2
        }

        getSiteList(params).then(res => {
          if (res.code === 200) {
            this.siteOptions = res.data?.data || []
          }
        }).catch(err => {
          console.error('获取站点列表失败:', err)
          this.siteOptions = []
        })
      },

      openRuleConfig(levelKey, configIndex) {
        // 验证是否已选择项目
        if (!this.form.projectId) {
          this.$message.warning('请先选择所属工程')
          return
        }

        // 验证是否已选择站点
        const siteId = this.form.responseLevels[levelKey].siteId
        const siteObjectId = this.form.responseLevels[levelKey].siteObjectId
        
        if (!siteId) {
          this.$message.warning('请先选择站点')
          return
        }

        if (!siteObjectId) {
          this.$message.warning('站点信息不完整，请重新选择站点')
          return
        }

        console.log('openRuleConfig called:', levelKey, configIndex)
        console.log('configThreshold ref:', this.$refs.configThreshold)

        if (this.$refs.configThreshold) {
          // 保存当前规则配置的上下文
          this.currentRuleConfig = {
            levelKey,
            configIndex
          }

          // 模拟一个预警规则对象，适配 ConfigThreshold 组件的接口
          const ruleData = {
            objectType: '1', // 假设为水位类型
            objectId: siteObjectId, // 使用站点的objectId
            projectId: this.form.projectId,
            objectRelId: siteId,
            siteId: siteId
          }

          this.$refs.configThreshold.handleConfigThreshold(ruleData)
        } else {
          console.error('ConfigThreshold ref is not available')
          this.$message.error('规则配置组件未正确加载')
        }
      },
      
      onRuleConfigSave(data) {
        // 处理规则配置保存逻辑
        console.log('规则配置保存:', data)

        if (this.currentRuleConfig && data) {
          const { levelKey, configIndex } = this.currentRuleConfig

          // 将规则配置数据保存到对应的配置项中
          this.form.responseLevels[levelKey].configs[configIndex].ruleConfig = data

          this.$message.success('规则配置保存成功')
          this.currentRuleConfig = null
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }

  ::v-deep .ant-tabs-tabpane {
    // height: calc(100vh - 160px);
  }

  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }

  ::v-deep .attaches {
    // margin-top: 100px;
  }

  .response-level-block {
    margin-bottom: 24px;
    
    .response-level-container {
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 16px;
      background-color: #fafafa;
    }
    
    .response-level-title {
      font-size: 16px;
      font-weight: 600;
      color: #111;
      margin-bottom: 16px;
      margin-top: 0;
    }

    .site-selection {
      margin-bottom: 16px;

      .site-label {
        display: inline-block;
        margin-bottom: 8px;
        font-weight: 500;
      }
    }

    .response-content {
      margin-bottom: 16px;

      .response-label {
        display: inline-block;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .response-textarea {
        width: 100%;
      }
    }
    
    .response-config {
      .config-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 12px;
        margin-top: 0;
      }
      
      .config-table {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        
        .table-header {
          display: flex;
          background-color: #fafafa;
          border-bottom: 1px solid #d9d9d9;
          
          .header-cell {
            font-weight: 600;
            color: #262626;
          }
        }
        
        .table-row {
          display: flex;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
        }
        
        .table-cell {
          padding: 8px 12px;
          border-right: 1px solid #f0f0f0;
          display: flex;
          align-items: center;

          &:first-child {
            width: 120px;
          }

          &:nth-child(2),
          &:nth-child(3) {
            flex: 1;
          }

          &:last-child {
            border-right: none;
          }

          .rule-config-btn {
            padding: 0;
            height: auto;
            font-size: 14px;
          }
        }
      }
    }
  }
</style>
