<template>
  <ant-modal
    :visible="open"
    modal-title="预警规则"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="600"
  >
    <div slot="content" class="threshold-content">
      <div class="threshold-table-wrapper">
        <div class="type">
          <p>逻辑符</p>
          <a-radio-group default-value="a" button-style="solid" v-model:value="form.logicOperator">
            <a-radio-button value="and">与</a-radio-button>
            <a-radio-button value="or">或</a-radio-button>
          </a-radio-group>
        </div>
        <div class="threshold-table-body">
          <div class="th-wrapper">
            <p class="th-col">指标名称</p>
            <p class="th-col">统计方式</p>
            <p class="th-col">统计周期</p>
            <p class="th-col">运算符</p>
            <p class="th-col">阈值类型</p>
            <p class="th-col">数值</p>
            <p class="th-col last-col">
              <a-button class="add-btn" type="primary" @click="addRule"><a-icon type="plus" /></a-button>
            </p>
          </div>
          <div class="td-wrapper" v-for="(item, ruleIndex) in form.rules" :key="item.id">
            <div class="td-col">
              <a-select
                allowClear
                v-model="item.indexCode"
                placeholder="请选择"
                v-if="objectType == '1'"
                @change="changeIndex(ruleIndex)"
              >
                <a-select-option v-for="(hydrology, index) in hydrologyIndexList" :key="index" :value="hydrology.key">
                  {{ hydrology.value }}
                </a-select-option>
              </a-select>
              <a-select allowClear v-model="item.indexCode" placeholder="请选择" v-else-if="objectType == '2'">
                <a-select-option
                  v-for="(deviceIndex, index) in deviceIndexList"
                  :key="index"
                  :value="deviceIndex.indexCode"
                >
                  {{ deviceIndex.indexName }}
                </a-select-option>
              </a-select>
              <div class="error"><span v-if="!item.indexCode && verify">指标不能为空</span></div>
            </div>
            <div class="td-col">
              <a-select allowClear v-model="item.statMethod" placeholder="请选择" @change="changeStatMethod(item)">
                <a-select-option
                  v-for="(statistical, index) in statisticalMethodsList"
                  :key="index"
                  :value="statistical.key"
                >
                  {{ statistical.value }}
                </a-select-option>
              </a-select>
              <div class="error"><span v-if="!item.statMethod && verify">统计方式不能为空</span></div>
            </div>
            <div class="td-col cycle">
              <a-input-number
                allow-clear
                id="inputNumber"
                placeholder="请输入"
                :disabled="item.statMethod == 'raw'"
                v-model="item.statPeriod"
                :min="1"
                :max="warnStartIntervalMax"
                :precision="0"
              />
              <span class="unit">分钟</span>
              <div class="error">
                <span v-if="item.statMethod != 'raw' && !item.statPeriod && verify">统计周期不能为空</span>
              </div>
            </div>
            <div class="td-col">
              <a-select allowClear v-model="item.compOperator" placeholder="请选择">
                <a-select-option v-for="(item, index) in operatorList" :key="index" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <div class="error"><span v-if="!item.compOperator && verify">运算符不能为空</span></div>
            </div>
            <div class="td-col">
              <a-select
                allowClear
                v-model="item.thresholdType"
                placeholder="请选择"
                @change="changeThresholdType(item, item.thresholdType)"
              >
                <a-select-option
                  :value="1"
                  :disabled="(item.thresholdType != '1' && item.indexCode != 'waterLevel') || projectId == undefined"
                >
                  恒定
                </a-select-option>
                <a-select-option
                  :value="2"
                  :disabled="(item.thresholdType != '1' && item.indexCode != 'waterLevel') || projectId == undefined"
                >
                  分时
                </a-select-option>
                <a-select-option :value="3">自定义</a-select-option>
              </a-select>
              <div class="error"><span v-if="!item.thresholdType && verify">阈值类型不能为空</span></div>
            </div>
            <div class="td-col other-col">
              <a-select
                allowClear
                v-model="item.thresholdRelId"
                placeholder="请选择"
                v-if="item.thresholdType == '1' && item.indexCode == 'waterLevel'"
              >
                <a-select-option v-for="(constant, index) in constantList" :key="index" :value="constant.thresholdKey">
                  {{ filterThresholdType(constant.thresholdCode) }}
                </a-select-option>
              </a-select>
              <a-select
                allowClear
                v-model="item.thresholdRelId"
                placeholder="请选择"
                v-if="item.thresholdType == '2' && item.indexCode == 'waterLevel'"
              >
                <a-select-option v-for="(time, index) in timeShareList" :key="index" :value="time.thresholdId">
                  {{ time.thresholdName }}
                </a-select-option>
              </a-select>
              <a-input-number
                allow-clear
                v-else-if="item.thresholdType == '3'"
                id="inputNumber"
                placeholder="请输入"
                v-model="item.thresholdValue"
              />
              <div class="error">
                <span
                  v-if="
                    !(
                      item.thresholdRelId ||
                      item.thresholdValue ||
                      item.thresholdValue == '' ||
                      item.thresholdValue == undefined
                    ) && verify
                  "
                >
                  数值不能为空
                </span>
              </div>
            </div>
            <div class="td-col last-col">
              <a-button class="del-btn" @click="deleteRule(ruleIndex)"><a-icon type="close" /></a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import {
    getDeviceIndex,
    getThresholdByType,
    getObjectDetails,
    saveObjectRule,
    getBaseSiteIndexList,
  } from '../services'
  import { getOptions, getValueByKey } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'ConfigureRules',
    components: { AntModal },
    props: {
      isEmergencyPlan: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        labelCol: { col: 4 },
        verify: false,
        warnStartIntervalMax: Number.MAX_SAFE_INTEGER,
        objectType: undefined,
        projectId: undefined,
        objectRelId: undefined,
        hydrologyIndexList: [],
        deviceIndexList: [],
        statisticalMethodsList: [],
        operatorList: [],
        thresholdTypeList: [
          { key: 1, value: '恒定' },
          { key: 2, value: '分时' },
          { key: 3, value: '自定义' },
        ],
        thresholdTypeOption: [],
        constantList: [],
        timeShareList: [],
        form: {
          // logicOperator: 'and',
          logicOperator: 'and',
          objectId: undefined,
          rules: [
            {
              compOperator: '',
              indexCode: '',
              statMethod: '',
              statPeriod: undefined,
              thresholdRelId: undefined,
              thresholdType: undefined,
              thresholdValue: undefined,
            },
          ],
        },
      }
    },
    methods: {
      handleConfigThreshold(row) {
        const { objectType, objectId, projectId, objectRelId } = JSON.parse(JSON.stringify(row))

        this.modalLoading = true
        this.open = true
        this.objectType = objectType
        this.projectId = projectId
        this.form.objectId = objectId
        this.objectRelId = objectRelId
        if (Number(objectType) == 1) {
          //水位
          getBaseSiteIndexList({ siteId: objectRelId }).then(res => {
            this.hydrologyIndexList = (res?.data || []).map(el => ({ key: el.indexCode, value: el.indexName }))
          })
          this.getDeviceOption()
        } else if (Number(objectType) == 2) {
          getDeviceIndex({ deviceId: this.objectRelId }).then(res => {
            this.deviceIndexList = res?.data[0].indexDetails
          })
        }
        getOptions('warnRuleStatMethod').then(res => {
          this.statisticalMethodsList = res?.data || []
        })
        getOptions('warnRuleComparison operator').then(res => {
          this.operatorList = res?.data || []
        })
        getOptions('projectThresholdType').then(res => {
          this.thresholdTypeOption = res?.data || []
        })
        getValueByKey('warn.stat.interval.max').then(res => {
          this.warnStartIntervalMax = +res.data
        })
        //详情
        getObjectDetails({ objectId: this.form.objectId }).then(res => {
          this.modalLoading = false
          if (res?.data?.rules?.length) {
            this.form.objectId = res?.data?.objectId
            this.form.logicOperator = res?.data?.logicOperator
            this.form.rules = res?.data?.rules
          }
        })
      },
      getDeviceOption() {
        //恒定
        getThresholdByType({ objectType: 2, projectId: this.objectRelId, thresholdType: 1 }).then(res => {
          this.constantList = res?.data || []
        })
        if (this.projectId) {
          //分时
          getThresholdByType({ objectType: this.objectType, projectId: this.projectId, thresholdType: 2 }).then(res => {
            this.timeShareList = res?.data || []
          })
        }
      },
      addRule() {
        this.form.rules.push({
          compOperator: '',
          indexCode: '',
          statMethod: '',
          statPeriod: undefined,
          thresholdRelId: undefined,
          thresholdType: undefined,
          thresholdValue: undefined,
        })
      },
      changeIndex(type) {
        if (type == 'waterLevel') {
          this.getDeviceOption()
        }
      },
      deleteRule(index) {
        this.form.rules.splice(index, 1)
      },
      changeStatMethod(row) {
        row.statPeriod = undefined
      },
      changeThresholdType(row, type) {
        row.thresholdRelId = undefined
        row.thresholdValue = undefined
      },
      /** 提交按钮 */
      submitForm() {
        this.verify = true

        this.loading = true
        let flag = this.form.rules.every(el => {
          return Object.keys(el).every(ele => {
            if (ele === 'statPeriod' && el['statMethod'] === 'raw') return true
            if (ele === 'thresholdRelId' && el['thresholdType'] == 3) return true
            if (ele === 'thresholdValue' && el['thresholdType'] != 3) return true
            if (el[ele] == 0) {
              return true
            }
            return !!el[ele]
          })
        })
        if (!flag) {
          this.loading = false
          return
        }

        // 如果是在应急预案中使用，不调用接口，直接返回数据
        if (this.isEmergencyPlan) {
          const ruleData = {
            logicOperator: this.form.logicOperator,
            rules: this.form.rules.map(rule => ({
              compOperator: rule.compOperator,
              indexCode: rule.indexCode,
              statMethod: rule.statMethod,
              statPeriod: rule.statPeriod,
              thresholdRelId: rule.thresholdRelId,
              thresholdType: rule.thresholdType,
              thresholdValue: rule.thresholdValue
            }))
          }

          this.loading = false
          this.open = false
          this.$emit('close')
          this.$emit('ok', ruleData)
          return
        }

        // 原有的接口调用逻辑
        saveObjectRule(this.form)
          .then(res => {
            if (res.code == 200) {
              this.$message.success('成功', 3)
              this.open = false
              this.$emit('close')
              this.$emit('ok')
            }
          })
          .catch(() => (this.loading = false))
      },
      cancel() {
        this.open = false
        this.$emit('close')
      },
      filterThresholdType(val) {
        let row = this.thresholdTypeOption.filter(item => {
          return val == item.key
        })
        return row[0].value
      },
    },
  }
</script>
<style lang="less" scoped>
  .threshold-content {
    padding: 10px;
    .threshold-table-wrapper {
      width: 100%;
      display: flex;
      .type {
        width: 110px;
      }
      .threshold-table-body {
        width: 100%;
        .th-wrapper,
        .td-wrapper {
          display: flex;
        }
      }
      .th-col,
      .td-col {
        width: 155px;
        text-align: center;
        margin-bottom: 10px;
        margin-right: 15px;

        .error {
          height: 18px;
          color: red;
          font-size: 12px;
        }
      }
      .other-col {
        width: 180px;
        .ant-input-number {
          width: 100%;
        }
      }
      .last-col {
        width: 30px;
        margin-right: 0;
      }
      ::v-deep .ant-select,
      ::v-deep .ant-input {
        width: 100% !important;
        margin-right: 10px;
      }
      .cycle {
        display: flex;
        flex-wrap: wrap;
        width: 130px;
        .unit {
          margin-left: auto;
          margin-top: 2px;
        }
      }
      ::v-deep .cycle .ant-input-affix-wrapper {
        width: 120px !important;
      }
      ::v-deep .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):first-child {
        margin-bottom: 10px;
      }
      .ant-btn {
        width: 24px;
        height: 24px;
        padding: 0;
      }
      .del-btn {
        color: #b9b9b9 !important;
      }
    }
  }
</style>
