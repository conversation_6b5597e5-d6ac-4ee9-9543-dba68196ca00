import request from '@/utils/request'

// 应急响应_应急预案-列表分页查询
export function getPlanPage(data) {
  return request({
    url: '/prjstd/plan/page',
    method: 'post',
    data
  })
}
// 增加
export function addPlan(data) {
  return request({
    url: '/prjstd/plan/add',
    method: 'post',
    data
  })
}
// 详情
export function getPlanById(params) {
  return request({
    url: '/prjstd/plan/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 更新
export function editPlan(data) {
  return request({
    url: '/prjstd/plan/update',
    method: 'post',
    data
  })
}
// 删除
export function deletePlan(params) {
  return request({
    url: '/prjstd/plan/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 获取站点列表
export function getSiteList(data) {
  return request({
    url: '/warn/object/site/page',
    method: 'post',
    data
  })
}
